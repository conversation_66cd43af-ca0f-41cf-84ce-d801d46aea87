import 'dart:convert';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gal/gal.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';
import 'package:toii_mesh/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_mesh/cubit/xmtp/xmtp_cubit.dart';
import 'package:toii_mesh/cubit/xmtp/xmtp_state.dart';
import 'package:toii_mesh/model/user/user_model.dart';

class QrMenuScreen extends StatefulWidget {
  const QrMenuScreen({super.key});

  @override
  State<QrMenuScreen> createState() => _QrMenuScreenState();
}

class _QrMenuScreenState extends State<QrMenuScreen> {
  final GlobalKey _qrKey = GlobalKey();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF3C27A6), // Purple background from Figma
              Color(0xFF6D55D7),
            ],
          ),
        ),
        child: Stack(
          children: [
            _buildBackgroundDecorations(),
            SafeArea(
              child: Column(
                children: [
                  _buildHeader(),
                  Expanded(
                    child: _buildContent(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackgroundDecorations() {
    return Positioned.fill(
      child: Stack(
        children: [
          // Map-like decorative elements from Figma
          Positioned(
            top: 0,
            left: -26,
            child: Opacity(
              opacity: 0.78,
              child: Container(
                width: 473,
                height: 275,
                decoration: BoxDecoration(
                  gradient: RadialGradient(
                    center: const Alignment(0.06, 0),
                    radius: 0.82,
                    colors: [
                      const Color(0xFFF4CAFF).withValues(alpha: 0.05),
                      const Color(0xFFCBCAFF).withValues(alpha: 0.39),
                      Colors.white.withValues(alpha: 0.82),
                    ],
                    stops: const [0.05, 0.39, 0.82],
                  ),
                ),
              ),
            ),
          ),
          // Additional decorative elements
          Positioned(
            bottom: 489,
            left: -26,
            child: Container(
              width: 459,
              height: 325,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: const Alignment(-0.42, -1),
                  end: const Alignment(0.42, 1),
                  colors: [
                    const Color(0xFFD1C1FF).withValues(alpha: 0.07),
                    const Color(0xFF6D55D7).withValues(alpha: 0.78),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.close,
                    size: 12,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          const Column(
            children: [
              Text(
                'My QR Code',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                  fontFamily: 'Manrope',
                ),
              ),
              SizedBox(height: 2),
            ],
          ),
          const SizedBox(width: 32), // Balance the close button
        ],
      ),
    );
  }

  Widget _buildContent() {
    return BlocBuilder<XmtpCubit, XmtpState>(
      builder: (context, xmtpState) {
        return BlocBuilder<ProfileCubit, ProfileState>(
          builder: (context, profileState) {
            final inboxId = context.read<XmtpCubit>().inboxId;
            final user = profileState.userModel;

            if (inboxId == null) {
              return const Center(
                child: Text(
                  'XMTP client not initialized',
                  style: TextStyle(color: Colors.white),
                ),
              );
            }

            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                children: [
                  const SizedBox(height: 58),
                  _buildQrCard(inboxId, user),
                  const SizedBox(height: 58),
                  _buildActionButtons(),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildQrCard(String inboxId, UserModel? user) {
    final qrData = jsonEncode({
      'type': 'meshii_connect',
      'inboxId': inboxId,
    });

    return Container(
      width: 358,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(61),
        child: Column(
          children: [
            _buildUserInfo(user),
            const SizedBox(height: 28),
            _buildQrCode(qrData),
            const SizedBox(height: 28),
            const Text(
              'Scan to connect on Meshii',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
                color: Color(0xFF2D216B),
                fontFamily: 'IBM Plex Sans',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserInfo(UserModel? user) {
    return Column(
      children: [
        Container(
          width: 68,
          height: 68,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 3),
          ),
          child: ClipOval(
            child: user?.avatarUrl != null
                ? Image.network(
                    user!.avatarUrl!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) =>
                        _buildDefaultAvatar(),
                  )
                : _buildDefaultAvatar(),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          user?.username ?? 'User',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w500,
            color: Color(0xFF292929),
            fontFamily: 'IBM Plex Sans',
          ),
        ),
      ],
    );
  }

  Widget _buildDefaultAvatar() {
    return Container(
      color: const Color(0xFFF0F0F0),
      child: const Icon(
        Icons.person,
        size: 32,
        color: Color(0xFF777777),
      ),
    );
  }

  Widget _buildQrCode(String data) {
    return RepaintBoundary(
      key: _qrKey,
      child: Container(
        width: 237,
        height: 237,
        color: Colors.black,
        child: QrImageView(
          data: data,
          version: QrVersions.auto,
          size: 237,
          backgroundColor: Colors.white,
          dataModuleStyle: const QrDataModuleStyle(
            dataModuleShape: QrDataModuleShape.square,
            color: Colors.black,
          ),
          eyeStyle: const QrEyeStyle(
            eyeShape: QrEyeShape.square,
            color: Colors.black,
          ),
          padding: const EdgeInsets.all(9),
          embeddedImage: const AssetImage('assets/images/logo.png'),
          embeddedImageStyle: const QrEmbeddedImageStyle(
            size: Size(40, 40),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildActionButton(
          icon: Icons.save_alt,
          label: 'Save QR code',
          onTap: _saveQrCode,
        ),
        _buildActionButton(
          icon: Icons.share,
          label: 'Share QR code',
          onTap: _shareQrCode,
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: _isLoading ? null : onTap,
      child: Container(
        width: 166,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
          borderRadius: BorderRadius.circular(24),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 24,
              color: Colors.white,
            ),
            const SizedBox(width: 9),
            Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
                color: Colors.white,
                fontFamily: 'IBM Plex Sans',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveQrCode() async {
    try {
      setState(() => _isLoading = true);

      final boundary =
          _qrKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
      if (boundary == null) return;

      final image = await boundary.toImage(pixelRatio: 3.0);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final pngBytes = byteData?.buffer.asUint8List();

      if (pngBytes != null) {
        // Create a temporary file
        final tempDir = Directory.systemTemp;
        final fileName =
            'meshii_qr_code_${DateTime.now().millisecondsSinceEpoch}.png';
        final file = await File('${tempDir.path}/$fileName').create();
        await file.writeAsBytes(pngBytes);

        // Save to gallery using gal
        await Gal.putImage(file.path, album: 'Meshii');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('QR code saved to gallery'),
              backgroundColor: Color(0xFF10B981),
            ),
          );
        }

        // Clean up temporary file
        await file.delete();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving QR code: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _shareQrCode() async {
    try {
      setState(() => _isLoading = true);

      final boundary =
          _qrKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
      if (boundary == null) return;

      final image = await boundary.toImage(pixelRatio: 3.0);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final pngBytes = byteData?.buffer.asUint8List();

      if (pngBytes != null) {
        final tempDir = Directory.systemTemp;
        final file = await File('${tempDir.path}/meshii_qr_code.png').create();
        await file.writeAsBytes(pngBytes);

        await Share.shareXFiles(
          [XFile(file.path)],
          text: 'Connect with me on Meshii!',
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sharing QR code: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
